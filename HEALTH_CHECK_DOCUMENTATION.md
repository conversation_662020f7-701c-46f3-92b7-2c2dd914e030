# MoneyTalk v2 Gateway - Health Check Documentation

## Overview

This document describes the comprehensive health checking system implemented for the MoneyTalk v2 microservices architecture. The gateway service acts as the central health monitoring hub for all downstream services.

## Health Check Endpoints

### 1. Standard Spring Boot Actuator Endpoints

#### `/actuator/health`
- **Purpose**: Main health check endpoint with detailed information
- **Method**: GET
- **Response Format**: JSON
- **Configuration**: Shows detailed health information for all components

**Example Response:**
```json
{
  "status": "UP",
  "components": {
    "auth-service": {
      "status": "UP",
      "details": {
        "service": "auth-service",
        "url": "http://auth:8100/actuator/health",
        "response": "OK",
        "timestamp": 1703123456789
      }
    },
    "user-service": {
      "status": "UP",
      "details": {
        "service": "user-service",
        "url": "http://user:8101/actuator/health",
        "response": "OK",
        "timestamp": 1703123456789
      }
    },
    "gateway": {
      "status": "UP",
      "details": {
        "memory.used": 134217728,
        "memory.max": 536870912,
        "memory.usage.percent": "25.00%",
        "cpu.load.percent": "15.50%",
        "available.processors": 8,
        "uptime": 300000,
        "timestamp": 1703123456789
      }
    }
  }
}
```

#### `/actuator/health/readiness`
- **Purpose**: Kubernetes readiness probe endpoint
- **Method**: GET
- **Components Checked**: 
  - readinessState
  - gateway
  - auth-service
  - user-service

#### `/actuator/health/liveness`
- **Purpose**: Kubernetes liveness probe endpoint
- **Method**: GET
- **Components Checked**:
  - livenessState
  - ping

### 2. Custom Health Aggregation Endpoints

#### `/health/services`
- **Purpose**: Comprehensive health status of all services
- **Method**: GET
- **Response Format**: JSON

**Example Response:**
```json
{
  "timestamp": "2024-01-01T12:00:00",
  "services": {
    "auth-service": {
      "status": "UP",
      "details": {
        "service": "auth-service",
        "url": "http://auth:8100/actuator/health",
        "response": "OK",
        "timestamp": 1703123456789
      }
    },
    "user-service": {
      "status": "UP",
      "details": {
        "service": "user-service",
        "url": "http://user:8101/actuator/health",
        "response": "OK",
        "timestamp": 1703123456789
      }
    },
    "gateway": {
      "status": "UP",
      "details": {
        "memory.usage.percent": "25.00%",
        "cpu.load.percent": "15.50%"
      }
    }
  },
  "overall-status": "UP",
  "healthy-services": 3,
  "total-services": 3
}
```

#### `/health/summary`
- **Purpose**: Quick health summary for monitoring dashboards
- **Method**: GET
- **Response Format**: JSON

**Example Response:**
```json
{
  "timestamp": "2024-01-01T12:00:00",
  "status": "HEALTHY",
  "healthy-services": 3,
  "total-services": 3,
  "health-percentage": 100
}
```

**Degraded Example:**
```json
{
  "timestamp": "2024-01-01T12:00:00",
  "status": "DEGRADED",
  "healthy-services": 2,
  "total-services": 3,
  "health-percentage": 66,
  "unhealthy-services": {
    "auth-service": "DOWN"
  }
}
```

### 3. Metrics and Monitoring Endpoints

#### `/actuator/metrics`
- **Purpose**: Application metrics
- **Method**: GET
- **Includes**: JVM metrics, HTTP metrics, custom business metrics

#### `/actuator/prometheus`
- **Purpose**: Prometheus-formatted metrics
- **Method**: GET
- **Use Case**: Integration with Prometheus monitoring

## Health Check Components

### 1. Service Health Indicators

#### AuthServiceHealthIndicator
- **Component Name**: `auth-service`
- **Check**: HTTP GET to `http://auth:8100/actuator/health`
- **Timeout**: 5 seconds
- **Retry**: 2 attempts with 500ms backoff
- **Status Codes**:
  - `UP`: Service responds successfully
  - `DOWN`: Service unreachable or returns error

#### UserServiceHealthIndicator
- **Component Name**: `user-service`
- **Check**: HTTP GET to `http://user:8101/actuator/health`
- **Timeout**: 5 seconds
- **Retry**: 2 attempts with 500ms backoff
- **Status Codes**:
  - `UP`: Service responds successfully
  - `DOWN`: Service unreachable or returns error

#### GatewayHealthIndicator
- **Component Name**: `gateway`
- **Checks**:
  - Memory usage (warns at 80%, fails at 90%)
  - CPU load (warns at 80%, fails at 90%)
  - System uptime
  - Available processors
- **Status Codes**:
  - `UP`: All system metrics within acceptable ranges
  - `DOWN`: Critical system resource thresholds exceeded

### 2. Circuit Breaker Integration

#### Circuit Breaker Health Monitoring
- **Auth Circuit Breaker**: `auth-cb`
- **User Circuit Breaker**: `user-cb`
- **Monitoring Frequency**: Every 30 seconds
- **Metrics Tracked**:
  - Circuit breaker state (CLOSED/OPEN/HALF_OPEN)
  - Failure rate percentage
  - Slow call rate percentage
  - Number of buffered calls
  - Number of failed calls
  - Number of successful calls

#### Circuit Breaker States
- **CLOSED**: Service is healthy, requests flow normally
- **OPEN**: Service is failing, requests are blocked
- **HALF_OPEN**: Service is being tested, limited requests allowed

## Configuration

### Health Check Configuration
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      show-components: always
      group:
        readiness:
          include: readinessState,gateway,auth-service,user-service
        liveness:
          include: livenessState,ping
  health:
    gateway:
      enabled: true
    circuitbreakers:
      enabled: true
```

### Circuit Breaker Configuration
```yaml
resilience4j:
  circuitbreaker:
    instances:
      auth-cb:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
```

## Monitoring Integration

### Kubernetes Health Checks
```yaml
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5
```
3. Test Full System
Once all services are configured:
# Start all services
```
docker-compose up -d
```
# Test health endpoints
```
curl http://localhost:8080/health/summary
curl http://localhost:8080/actuator/health
```
🔧 Available Health Endpoints
|Endpoint|Purpose|Use Case|
|---|---|---|
/actuator/health	Complete health details	Development, debugging
/health/summary	Quick health overview	Monitoring dashboards
/health/services	Service-by-service status	Operations, troubleshooting
/actuator/health/readiness	Kubernetes readiness	Container orchestration
/actuator/health/liveness	Kubernetes liveness	Container orchestration
/actuator/prometheus	Metrics for monitoring	Prometheus/
### Prometheus Monitoring
- Metrics endpoint: `/actuator/prometheus`
- Custom metrics for service health status
- Circuit breaker metrics
- JVM and system metrics

## Troubleshooting

### Common Issues

1. **Service Shows DOWN**
   - Check if the service is running
   - Verify network connectivity
   - Check service logs for errors
   - Verify actuator endpoints are enabled on downstream services

2. **Circuit Breaker OPEN**
   - Check downstream service health
   - Review failure rate and slow call metrics
   - Wait for automatic recovery or manually reset

3. **High Memory/CPU Warnings**
   - Monitor application performance
   - Check for memory leaks
   - Scale resources if needed

### Health Check Best Practices

1. **Downstream Services**: Ensure all services have actuator health endpoints
2. **Timeouts**: Configure appropriate timeouts for health checks
3. **Monitoring**: Set up alerts based on health check results
4. **Documentation**: Keep health check documentation updated
5. **Testing**: Regularly test health check functionality
