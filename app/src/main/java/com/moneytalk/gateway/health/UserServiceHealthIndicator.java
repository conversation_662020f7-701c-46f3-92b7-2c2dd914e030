package com.moneytalk.gateway.health;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.concurrent.TimeoutException;

@Component("user-service")
public class UserServiceHealthIndicator implements HealthIndicator {

    private final WebClient webClient;
    private final String userServiceUrl;

    public UserServiceHealthIndicator() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.userServiceUrl = "http://user:8101";
    }

    @Override
    public Health health() {
        try {
            return checkUserServiceHealth().block(Duration.ofSeconds(5));
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("service", "user-service")
                    .withDetail("url", userServiceUrl + "/actuator/health")
                    .build();
        }
    }

    private Mono<Health> checkUserServiceHealth() {
        return webClient.get()
                .uri(userServiceUrl + "/actuator/health")
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> Health.up()
                        .withDetail("service", "user-service")
                        .withDetail("url", userServiceUrl + "/actuator/health")
                        .withDetail("response", "OK")
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build())
                .retryWhen(Retry.backoff(2, Duration.ofMillis(500)))
                .onErrorReturn(TimeoutException.class, Health.down()
                        .withDetail("error", "Timeout connecting to user service")
                        .withDetail("service", "user-service")
                        .withDetail("url", userServiceUrl + "/actuator/health")
                        .build())
                .onErrorReturn(Health.down()
                        .withDetail("error", "Failed to connect to user service")
                        .withDetail("service", "user-service")
                        .withDetail("url", userServiceUrl + "/actuator/health")
                        .build());
    }
}
