package com.moneytalk.gateway.health;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;

@Component("gateway")
public class GatewayHealthIndicator implements HealthIndicator {

    private final MemoryMXBean memoryBean;
    private final OperatingSystemMXBean osBean;

    public GatewayHealthIndicator() {
        this.memoryBean = ManagementFactory.getMemoryMXBean();
        this.osBean = ManagementFactory.getOperatingSystemMXBean();
    }

    @Override
    public Health health() {
        try {
            // Check memory usage
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            double memoryUsagePercent = (double) usedMemory / maxMemory * 100;

            // Check CPU load (using system load average as fallback)
            double cpuLoad = osBean.getSystemLoadAverage();
            if (cpuLoad < 0) {
                cpuLoad = 0; // System load average not available on this platform
            }

            Health.Builder healthBuilder = Health.up();

            // Add system metrics
            healthBuilder
                    .withDetail("memory.used", usedMemory)
                    .withDetail("memory.max", maxMemory)
                    .withDetail("memory.usage.percent", String.format("%.2f%%", memoryUsagePercent))
                    .withDetail("system.load.average", String.format("%.2f", cpuLoad))
                    .withDetail("available.processors", osBean.getAvailableProcessors())
                    .withDetail("uptime", ManagementFactory.getRuntimeMXBean().getUptime())
                    .withDetail("timestamp", System.currentTimeMillis());

            // Health checks based on thresholds
            if (memoryUsagePercent > 90) {
                healthBuilder.down().withDetail("warning", "High memory usage: " + String.format("%.2f%%", memoryUsagePercent));
            } else if (memoryUsagePercent > 80) {
                healthBuilder.withDetail("warning", "Memory usage approaching limit: " + String.format("%.2f%%", memoryUsagePercent));
            }

            // System load average threshold (typically compared to number of processors)
            int processors = osBean.getAvailableProcessors();
            if (cpuLoad > processors * 2) {
                healthBuilder.down().withDetail("warning", "High system load: " + String.format("%.2f", cpuLoad));
            } else if (cpuLoad > processors * 1.5) {
                healthBuilder.withDetail("warning", "System load is high: " + String.format("%.2f", cpuLoad));
            }

            return healthBuilder.build();

        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", "Failed to check gateway health: " + e.getMessage())
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
        }
    }
}
