package com.moneytalk.gateway.health;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.concurrent.TimeoutException;

@Component("auth-service")
public class AuthServiceHealthIndicator implements HealthIndicator {

    private final WebClient webClient;
    private final String authServiceUrl;

    public AuthServiceHealthIndicator() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.authServiceUrl = "http://auth:8100";
    }

    @Override
    public Health health() {
        try {
            return checkAuthServiceHealth().block(Duration.ofSeconds(5));
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("service", "auth-service")
                    .withDetail("url", authServiceUrl + "/actuator/health")
                    .build();
        }
    }

    private Mono<Health> checkAuthServiceHealth() {
        return webClient.get()
                .uri(authServiceUrl + "/actuator/health")
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> Health.up()
                        .withDetail("service", "auth-service")
                        .withDetail("url", authServiceUrl + "/actuator/health")
                        .withDetail("response", "OK")
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build())
                .retryWhen(Retry.backoff(2, Duration.ofMillis(500)))
                .onErrorReturn(TimeoutException.class, Health.down()
                        .withDetail("error", "Timeout connecting to auth service")
                        .withDetail("service", "auth-service")
                        .withDetail("url", authServiceUrl + "/actuator/health")
                        .build())
                .onErrorReturn(Health.down()
                        .withDetail("error", "Failed to connect to auth service")
                        .withDetail("service", "auth-service")
                        .withDetail("url", authServiceUrl + "/actuator/health")
                        .build());
    }
}
