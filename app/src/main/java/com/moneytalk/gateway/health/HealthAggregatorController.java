package com.moneytalk.gateway.health;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/health")
public class HealthAggregatorController {

    private final AuthServiceHealthIndicator authHealthIndicator;
    private final UserServiceHealthIndicator userHealthIndicator;
    private final GatewayHealthIndicator gatewayHealthIndicator;

    public HealthAggregatorController(
            AuthServiceHealthIndicator authHealthIndicator,
            UserServiceHealthIndicator userHealthIndicator,
            GatewayHealthIndicator gatewayHealthIndicator) {
        this.authHealthIndicator = authHealthIndicator;
        this.userHealthIndicator = userHealthIndicator;
        this.gatewayHealthIndicator = gatewayHealthIndicator;
    }

    @GetMapping("/services")
    public Mono<Map<String, Object>> getAllServicesHealth() {
        return Mono.fromCallable(() -> {
            Map<String, Object> healthStatus = new HashMap<>();
            
            // Check all services
            Health authHealth = authHealthIndicator.health();
            Health userHealth = userHealthIndicator.health();
            Health gatewayHealth = gatewayHealthIndicator.health();
            
            // Aggregate results
            healthStatus.put("timestamp", LocalDateTime.now());
            healthStatus.put("services", Map.of(
                "auth-service", createServiceStatus(authHealth),
                "user-service", createServiceStatus(userHealth),
                "gateway", createServiceStatus(gatewayHealth)
            ));
            
            // Overall status
            boolean allHealthy = authHealth.getStatus().getCode().equals("UP") &&
                               userHealth.getStatus().getCode().equals("UP") &&
                               gatewayHealth.getStatus().getCode().equals("UP");
            
            healthStatus.put("overall-status", allHealthy ? "UP" : "DOWN");
            healthStatus.put("healthy-services", countHealthyServices(authHealth, userHealth, gatewayHealth));
            healthStatus.put("total-services", 3);
            
            return healthStatus;
        });
    }

    @GetMapping("/summary")
    public Mono<Map<String, Object>> getHealthSummary() {
        return Mono.fromCallable(() -> {
            Map<String, Object> summary = new HashMap<>();
            
            Health authHealth = authHealthIndicator.health();
            Health userHealth = userHealthIndicator.health();
            Health gatewayHealth = gatewayHealthIndicator.health();
            
            int healthyCount = countHealthyServices(authHealth, userHealth, gatewayHealth);
            boolean allHealthy = healthyCount == 3;
            
            summary.put("timestamp", LocalDateTime.now());
            summary.put("status", allHealthy ? "HEALTHY" : "DEGRADED");
            summary.put("healthy-services", healthyCount);
            summary.put("total-services", 3);
            summary.put("health-percentage", (healthyCount * 100) / 3);
            
            if (!allHealthy) {
                summary.put("unhealthy-services", getUnhealthyServices(authHealth, userHealth, gatewayHealth));
            }
            
            return summary;
        });
    }

    private Map<String, Object> createServiceStatus(Health health) {
        Map<String, Object> status = new HashMap<>();
        status.put("status", health.getStatus().getCode());
        status.put("details", health.getDetails());
        return status;
    }

    private int countHealthyServices(Health... healths) {
        int count = 0;
        for (Health health : healths) {
            if (health.getStatus().getCode().equals("UP")) {
                count++;
            }
        }
        return count;
    }

    private Map<String, String> getUnhealthyServices(Health authHealth, Health userHealth, Health gatewayHealth) {
        Map<String, String> unhealthy = new HashMap<>();
        
        if (!authHealth.getStatus().getCode().equals("UP")) {
            unhealthy.put("auth-service", authHealth.getStatus().getCode());
        }
        if (!userHealth.getStatus().getCode().equals("UP")) {
            unhealthy.put("user-service", userHealth.getStatus().getCode());
        }
        if (!gatewayHealth.getStatus().getCode().equals("UP")) {
            unhealthy.put("gateway", gatewayHealth.getStatus().getCode());
        }
        
        return unhealthy;
    }
}
