package com.moneytalk.gateway.config;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Configuration
@EnableScheduling
public class HealthCheckConfig {

    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final ConcurrentMap<String, Health> healthCache = new ConcurrentHashMap<>();

    public HealthCheckConfig(CircuitBreakerRegistry circuitBreakerRegistry) {
        this.circuitBreakerRegistry = circuitBreakerRegistry;
    }

    @Bean
    public WebClient healthCheckWebClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
    }

    @Bean("ping")
    public HealthIndicator pingHealthIndicator() {
        return () -> Health.up()
                .withDetail("service", "gateway")
                .withDetail("status", "ping successful")
                .withDetail("timestamp", System.currentTimeMillis())
                .build();
    }

    @Scheduled(fixedRate = 30000) // Every 30 seconds
    public void performHealthChecks() {
        // Check circuit breaker states
        checkCircuitBreakerHealth("auth-cb", "auth-service");
        checkCircuitBreakerHealth("user-cb", "user-service");
    }

    private void checkCircuitBreakerHealth(String circuitBreakerName, String serviceName) {
        try {
            CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName);
            CircuitBreaker.State state = circuitBreaker.getState();
            
            Health.Builder healthBuilder = Health.up();
            
            switch (state) {
                case CLOSED:
                    healthBuilder.withDetail("circuit-breaker", "CLOSED - Service is healthy");
                    break;
                case OPEN:
                    healthBuilder = Health.down()
                            .withDetail("circuit-breaker", "OPEN - Service is failing");
                    break;
                case HALF_OPEN:
                    healthBuilder.withDetail("circuit-breaker", "HALF_OPEN - Service is being tested");
                    break;
                default:
                    healthBuilder.withDetail("circuit-breaker", "UNKNOWN");
            }
            
            healthBuilder
                    .withDetail("service", serviceName)
                    .withDetail("failure-rate", String.format("%.2f%%", circuitBreaker.getMetrics().getFailureRate()))
                    .withDetail("slow-call-rate", String.format("%.2f%%", circuitBreaker.getMetrics().getSlowCallRate()))
                    .withDetail("buffered-calls", circuitBreaker.getMetrics().getNumberOfBufferedCalls())
                    .withDetail("failed-calls", circuitBreaker.getMetrics().getNumberOfFailedCalls())
                    .withDetail("successful-calls", circuitBreaker.getMetrics().getNumberOfSuccessfulCalls())
                    .withDetail("timestamp", System.currentTimeMillis());
            
            healthCache.put(serviceName + "-circuit-breaker", healthBuilder.build());
            
        } catch (Exception e) {
            Health errorHealth = Health.down()
                    .withDetail("error", "Failed to check circuit breaker: " + e.getMessage())
                    .withDetail("service", serviceName)
                    .withDetail("circuit-breaker", circuitBreakerName)
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
            
            healthCache.put(serviceName + "-circuit-breaker", errorHealth);
        }
    }

    public ConcurrentMap<String, Health> getHealthCache() {
        return healthCache;
    }
}
