server:
  port: 8080

spring:
  application:
    name: gateway
  cloud:
    gateway:
      default-filters:
        - PreserveHostHeader
      routes:
        - id: auth
          uri: http://auth:8100
          predicates:
            - Path=/auth/**
          filters:
            - RewritePath=/auth/(?<segment>.*), /${segment}
        - id: user
          uri: http://user:8101
          predicates:
            - Path=/users/**
          filters:
            - RewritePath=/users/(?<segment>.*), /${segment}
  main:
    web-application-type: reactive

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      probes:
        enabled: true
