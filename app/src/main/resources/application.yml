server:
  port: 8080

spring:
  application:
    name: gateway
  cloud:
    gateway:
      default-filters:
        - PreserveHostHeader
      routes:
        - id: auth
          uri: http://auth:8100
          predicates:
            - Path=/auth/**
          filters:
            - RewritePath=/auth/(?<segment>.*), /${segment}
            - CircuitBreaker=auth-cb
        - id: user
          uri: http://user:8101
          predicates:
            - Path=/users/**
          filters:
            - RewritePath=/users/(?<segment>.*), /${segment}
            - CircuitBreaker=user-cb
      httpclient:
        connect-timeout: 5000
        response-timeout: 10s
  main:
    web-application-type: reactive

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
      show-details: always
      show-components: always
      group:
        readiness:
          include: readinessState,gateway,auth-service,user-service
        liveness:
          include: livenessState,ping
  health:
    gateway:
      enabled: true
    circuitbreakers:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      auth-cb:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
      user-cb:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
