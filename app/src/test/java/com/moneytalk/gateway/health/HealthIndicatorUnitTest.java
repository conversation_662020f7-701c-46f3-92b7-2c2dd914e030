package com.moneytalk.gateway.health;

import org.junit.jupiter.api.Test;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.Status;

import static org.junit.jupiter.api.Assertions.*;

class HealthIndicatorUnitTest {

    @Test
    void testGatewayHealthIndicator_ReturnsHealthyStatus() {
        GatewayHealthIndicator indicator = new GatewayHealthIndicator();
        Health health = indicator.health();
        
        assertNotNull(health);
        assertNotNull(health.getStatus());
        
        // Verify required details are present
        assertTrue(health.getDetails().containsKey("memory.used"));
        assertTrue(health.getDetails().containsKey("memory.max"));
        assertTrue(health.getDetails().containsKey("memory.usage.percent"));
        assertTrue(health.getDetails().containsKey("cpu.load.percent"));
        assertTrue(health.getDetails().containsKey("available.processors"));
        assertTrue(health.getDetails().containsKey("uptime"));
        assertTrue(health.getDetails().containsKey("timestamp"));
        
        // Verify data types
        assertTrue(health.getDetails().get("memory.used") instanceof Long);
        assertTrue(health.getDetails().get("memory.max") instanceof Long);
        assertTrue(health.getDetails().get("memory.usage.percent") instanceof String);
        assertTrue(health.getDetails().get("cpu.load.percent") instanceof String);
        assertTrue(health.getDetails().get("available.processors") instanceof Integer);
        assertTrue(health.getDetails().get("uptime") instanceof Long);
        assertTrue(health.getDetails().get("timestamp") instanceof Long);
        
        // Verify memory usage percentage format
        String memoryUsage = (String) health.getDetails().get("memory.usage.percent");
        assertTrue(memoryUsage.endsWith("%"));
        
        // Verify CPU load percentage format
        String cpuLoad = (String) health.getDetails().get("cpu.load.percent");
        assertTrue(cpuLoad.endsWith("%"));
        
        // Verify available processors is positive
        Integer processors = (Integer) health.getDetails().get("available.processors");
        assertTrue(processors > 0);
        
        // Verify uptime is positive
        Long uptime = (Long) health.getDetails().get("uptime");
        assertTrue(uptime >= 0);
        
        // Verify timestamp is recent (within last minute)
        Long timestamp = (Long) health.getDetails().get("timestamp");
        long currentTime = System.currentTimeMillis();
        assertTrue(Math.abs(currentTime - timestamp) < 60000); // Within 1 minute
    }

    @Test
    void testAuthServiceHealthIndicator_Structure() {
        AuthServiceHealthIndicator indicator = new AuthServiceHealthIndicator();
        Health health = indicator.health();
        
        assertNotNull(health);
        assertNotNull(health.getStatus());
        
        // Verify required details are present
        assertTrue(health.getDetails().containsKey("service"));
        assertTrue(health.getDetails().containsKey("url"));
        
        // Verify service name
        assertEquals("auth-service", health.getDetails().get("service"));
        
        // Verify URL format
        String url = (String) health.getDetails().get("url");
        assertTrue(url.contains("auth:8100"));
        assertTrue(url.contains("/actuator/health"));
        
        // Since we can't guarantee the auth service is running in tests,
        // we just verify the structure is correct
        // The status will likely be DOWN, but that's expected in test environment
    }

    @Test
    void testUserServiceHealthIndicator_Structure() {
        UserServiceHealthIndicator indicator = new UserServiceHealthIndicator();
        Health health = indicator.health();
        
        assertNotNull(health);
        assertNotNull(health.getStatus());
        
        // Verify required details are present
        assertTrue(health.getDetails().containsKey("service"));
        assertTrue(health.getDetails().containsKey("url"));
        
        // Verify service name
        assertEquals("user-service", health.getDetails().get("service"));
        
        // Verify URL format
        String url = (String) health.getDetails().get("url");
        assertTrue(url.contains("user:8101"));
        assertTrue(url.contains("/actuator/health"));
        
        // Since we can't guarantee the user service is running in tests,
        // we just verify the structure is correct
        // The status will likely be DOWN, but that's expected in test environment
    }

    @Test
    void testHealthIndicator_TimestampIsRecent() {
        GatewayHealthIndicator indicator = new GatewayHealthIndicator();
        
        long beforeCall = System.currentTimeMillis();
        Health health = indicator.health();
        long afterCall = System.currentTimeMillis();
        
        Long timestamp = (Long) health.getDetails().get("timestamp");
        assertNotNull(timestamp);
        assertTrue(timestamp >= beforeCall);
        assertTrue(timestamp <= afterCall);
    }

    @Test
    void testHealthIndicator_ConsistentResults() {
        GatewayHealthIndicator indicator = new GatewayHealthIndicator();
        
        Health health1 = indicator.health();
        Health health2 = indicator.health();
        
        // Both calls should succeed
        assertNotNull(health1);
        assertNotNull(health2);
        
        // Both should have the same structure
        assertEquals(health1.getDetails().keySet(), health2.getDetails().keySet());
        
        // Available processors should be the same
        assertEquals(
            health1.getDetails().get("available.processors"),
            health2.getDetails().get("available.processors")
        );
        
        // Memory max should be the same (or very close)
        Long maxMemory1 = (Long) health1.getDetails().get("memory.max");
        Long maxMemory2 = (Long) health2.getDetails().get("memory.max");
        assertEquals(maxMemory1, maxMemory2);
    }

    @Test
    void testHealthIndicator_MemoryUsageCalculation() {
        GatewayHealthIndicator indicator = new GatewayHealthIndicator();
        Health health = indicator.health();
        
        Long usedMemory = (Long) health.getDetails().get("memory.used");
        Long maxMemory = (Long) health.getDetails().get("memory.max");
        String usagePercent = (String) health.getDetails().get("memory.usage.percent");
        
        assertNotNull(usedMemory);
        assertNotNull(maxMemory);
        assertNotNull(usagePercent);
        
        // Verify memory values are reasonable
        assertTrue(usedMemory >= 0);
        assertTrue(maxMemory > 0);
        assertTrue(usedMemory <= maxMemory);
        
        // Verify percentage calculation
        double expectedPercent = (double) usedMemory / maxMemory * 100;
        String expectedPercentStr = String.format("%.2f%%", expectedPercent);
        assertEquals(expectedPercentStr, usagePercent);
    }

    @Test
    void testHealthIndicator_StatusDetermination() {
        GatewayHealthIndicator indicator = new GatewayHealthIndicator();
        Health health = indicator.health();
        
        Status status = health.getStatus();
        assertNotNull(status);
        
        // Status should be either UP or DOWN
        assertTrue(status.equals(Status.UP) || status.equals(Status.DOWN));
        
        // If status is DOWN, there should be a warning in details
        if (status.equals(Status.DOWN)) {
            assertTrue(health.getDetails().containsKey("warning"));
        }
    }
}
