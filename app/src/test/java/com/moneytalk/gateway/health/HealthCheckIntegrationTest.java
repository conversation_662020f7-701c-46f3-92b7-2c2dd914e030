package com.moneytalk.gateway.health;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "management.endpoints.web.exposure.include=health,info,metrics",
    "management.endpoint.health.show-details=always"
})
class HealthCheckIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testMainHealthEndpoint() throws Exception {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/actuator/health", String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        JsonNode healthResponse = objectMapper.readTree(response.getBody());
        assertNotNull(healthResponse);
        assertTrue(healthResponse.has("status"));
        assertTrue(healthResponse.has("components"));
        
        // Check that our custom health indicators are present
        JsonNode components = healthResponse.get("components");
        assertTrue(components.has("gateway"));
        assertTrue(components.has("ping"));
    }

    @Test
    void testReadinessProbe() {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/actuator/health/readiness", String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void testLivenessProbe() {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/actuator/health/liveness", String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void testCustomHealthServicesEndpoint() throws Exception {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/health/services", String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        JsonNode healthResponse = objectMapper.readTree(response.getBody());
        assertNotNull(healthResponse);
        assertTrue(healthResponse.has("timestamp"));
        assertTrue(healthResponse.has("services"));
        assertTrue(healthResponse.has("overall-status"));
        assertTrue(healthResponse.has("healthy-services"));
        assertTrue(healthResponse.has("total-services"));
        
        // Check services structure
        JsonNode services = healthResponse.get("services");
        assertTrue(services.has("gateway"));
        assertTrue(services.has("auth-service"));
        assertTrue(services.has("user-service"));
        
        // Verify total services count
        assertEquals(3, healthResponse.get("total-services").asInt());
    }

    @Test
    void testCustomHealthSummaryEndpoint() throws Exception {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/health/summary", String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        JsonNode summaryResponse = objectMapper.readTree(response.getBody());
        assertNotNull(summaryResponse);
        assertTrue(summaryResponse.has("timestamp"));
        assertTrue(summaryResponse.has("status"));
        assertTrue(summaryResponse.has("healthy-services"));
        assertTrue(summaryResponse.has("total-services"));
        assertTrue(summaryResponse.has("health-percentage"));
        
        // Verify status is either HEALTHY or DEGRADED
        String status = summaryResponse.get("status").asText();
        assertTrue(status.equals("HEALTHY") || status.equals("DEGRADED"));
        
        // Verify health percentage is between 0 and 100
        int healthPercentage = summaryResponse.get("health-percentage").asInt();
        assertTrue(healthPercentage >= 0 && healthPercentage <= 100);
    }

    @Test
    void testGatewayHealthIndicator() {
        GatewayHealthIndicator indicator = new GatewayHealthIndicator();
        var health = indicator.health();
        
        assertNotNull(health);
        assertNotNull(health.getStatus());
        assertTrue(health.getDetails().containsKey("memory.used"));
        assertTrue(health.getDetails().containsKey("memory.max"));
        assertTrue(health.getDetails().containsKey("memory.usage.percent"));
        assertTrue(health.getDetails().containsKey("cpu.load.percent"));
        assertTrue(health.getDetails().containsKey("available.processors"));
        assertTrue(health.getDetails().containsKey("uptime"));
        assertTrue(health.getDetails().containsKey("timestamp"));
    }

    @Test
    void testAuthServiceHealthIndicator() {
        AuthServiceHealthIndicator indicator = new AuthServiceHealthIndicator();
        var health = indicator.health();
        
        assertNotNull(health);
        assertNotNull(health.getStatus());
        assertTrue(health.getDetails().containsKey("service"));
        assertTrue(health.getDetails().containsKey("url"));
        
        // Since auth service might not be running in test, we expect it to be DOWN
        // but the health check should still work
        assertEquals("auth-service", health.getDetails().get("service"));
    }

    @Test
    void testUserServiceHealthIndicator() {
        UserServiceHealthIndicator indicator = new UserServiceHealthIndicator();
        var health = indicator.health();
        
        assertNotNull(health);
        assertNotNull(health.getStatus());
        assertTrue(health.getDetails().containsKey("service"));
        assertTrue(health.getDetails().containsKey("url"));
        
        // Since user service might not be running in test, we expect it to be DOWN
        // but the health check should still work
        assertEquals("user-service", health.getDetails().get("service"));
    }

    @Test
    void testMetricsEndpoint() {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/actuator/metrics", String.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().contains("names"));
    }

    @Test
    void testHealthServicesEndpoint_ServiceStructure() throws Exception {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/health/services", String.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        JsonNode healthResponse = objectMapper.readTree(response.getBody());
        JsonNode services = healthResponse.get("services");

        // Test gateway service structure
        JsonNode gatewayService = services.get("gateway");
        assertNotNull(gatewayService);
        assertTrue(gatewayService.has("status"));
        assertTrue(gatewayService.has("details"));

        // Test auth service structure
        JsonNode authService = services.get("auth-service");
        assertNotNull(authService);
        assertTrue(authService.has("status"));
        assertTrue(authService.has("details"));

        // Test user service structure
        JsonNode userService = services.get("user-service");
        assertNotNull(userService);
        assertTrue(userService.has("status"));
        assertTrue(userService.has("details"));
    }

    @Test
    void testHealthSummaryEndpoint_DegradedScenario() throws Exception {
        // This test verifies the summary endpoint handles degraded scenarios correctly
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/health/summary", String.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        JsonNode summaryResponse = objectMapper.readTree(response.getBody());

        // If some services are down (which is expected in test environment),
        // verify the summary reflects this correctly
        int healthyServices = summaryResponse.get("healthy-services").asInt();
        int totalServices = summaryResponse.get("total-services").asInt();

        assertTrue(healthyServices >= 0);
        assertTrue(healthyServices <= totalServices);
        assertEquals(3, totalServices);

        // If not all services are healthy, should have unhealthy-services field
        if (healthyServices < totalServices) {
            assertTrue(summaryResponse.has("unhealthy-services"));
            assertEquals("DEGRADED", summaryResponse.get("status").asText());
        } else {
            assertEquals("HEALTHY", summaryResponse.get("status").asText());
        }
    }
}
