# Microservice Health Check Configuration Guide

This document provides configuration templates for setting up health checks in your other microservices (auth and user services).

## Auth Service Configuration

### application.yml
```yaml
server:
  port: 8100

spring:
  application:
    name: auth-service

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  health:
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Database configuration (example for MySQL)
spring:
  datasource:
    url: *******************************
    username: ${DB_USERNAME:auth_user}
    password: ${DB_PASSWORD:auth_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
```

### build.gradle dependencies
```gradle
dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'mysql:mysql-connector-java'
    implementation 'io.micrometer:micrometer-registry-prometheus'
}
```

### Custom Health Indicator for Auth Service
```java
package com.moneytalk.auth.health;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import javax.sql.DataSource;
import java.sql.Connection;

@Component("auth-business")
public class AuthBusinessHealthIndicator implements HealthIndicator {

    @Autowired
    private DataSource dataSource;

    @Override
    public Health health() {
        try {
            // Check database connectivity
            try (Connection connection = dataSource.getConnection()) {
                if (connection.isValid(5)) {
                    return Health.up()
                            .withDetail("database", "Connected")
                            .withDetail("service", "auth-service")
                            .withDetail("timestamp", System.currentTimeMillis())
                            .build();
                } else {
                    return Health.down()
                            .withDetail("database", "Connection invalid")
                            .withDetail("service", "auth-service")
                            .build();
                }
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("database", "Connection failed: " + e.getMessage())
                    .withDetail("service", "auth-service")
                    .build();
        }
    }
}
```

## User Service Configuration

### application.yml
```yaml
server:
  port: 8101

spring:
  application:
    name: user-service

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  health:
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Database configuration (example for MySQL)
spring:
  datasource:
    url: *******************************
    username: ${DB_USERNAME:user_user}
    password: ${DB_PASSWORD:user_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
```

### build.gradle dependencies
```gradle
dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'mysql:mysql-connector-java'
    implementation 'io.micrometer:micrometer-registry-prometheus'
}
```

### Custom Health Indicator for User Service
```java
package com.moneytalk.user.health;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import javax.sql.DataSource;
import java.sql.Connection;

@Component("user-business")
public class UserBusinessHealthIndicator implements HealthIndicator {

    @Autowired
    private DataSource dataSource;

    @Override
    public Health health() {
        try {
            // Check database connectivity
            try (Connection connection = dataSource.getConnection()) {
                if (connection.isValid(5)) {
                    return Health.up()
                            .withDetail("database", "Connected")
                            .withDetail("service", "user-service")
                            .withDetail("timestamp", System.currentTimeMillis())
                            .build();
                } else {
                    return Health.down()
                            .withDetail("database", "Connection invalid")
                            .withDetail("service", "user-service")
                            .build();
                }
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("database", "Connection failed: " + e.getMessage())
                    .withDetail("service", "user-service")
                    .build();
        }
    }
}
```

## Docker Configuration

### Dockerfile Health Check
Add health checks to your Dockerfiles:

```dockerfile
# Auth Service Dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY build/libs/*.jar app.jar

EXPOSE 8100

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8100/actuator/health || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```dockerfile
# User Service Dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY build/libs/*.jar app.jar

EXPOSE 8101

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8101/actuator/health || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
```

## Docker Compose Configuration

### docker-compose.yml (for moneytalk-v2-infra)
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: moneytalk_db
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  auth:
    build: ../moneytalk-v2-auth
    ports:
      - "8100:8100"
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      DB_USERNAME: root
      DB_PASSWORD: rootpassword
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  user:
    build: ../moneytalk-v2-user
    ports:
      - "8101:8101"
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      DB_USERNAME: root
      DB_PASSWORD: rootpassword
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8101/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  gateway:
    build: ../moneytalk-v2-gateway
    ports:
      - "8080:8080"
    depends_on:
      auth:
        condition: service_healthy
      user:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
```

## Kubernetes Configuration

### Health Check Probes
```yaml
# auth-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: moneytalk/auth-service:latest
        ports:
        - containerPort: 8100
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8100
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8100
          initialDelaySeconds: 30
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
```

## Monitoring Integration

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'moneytalk-services'
    static_configs:
      - targets: ['gateway:8080', 'auth:8100', 'user:8101']
    metrics_path: /actuator/prometheus
    scrape_interval: 30s
```

### Grafana Dashboard
Create dashboards to monitor:
- Service health status
- Response times
- Error rates
- Circuit breaker states
- JVM metrics
- Database connection health

## Testing Health Checks

### Health Check Test Script
```bash
#!/bin/bash

# Test all service health endpoints
services=("gateway:8080" "auth:8100" "user:8101")

for service in "${services[@]}"; do
    echo "Testing $service..."
    
    # Test main health endpoint
    curl -f "http://$service/actuator/health" || echo "Health check failed for $service"
    
    # Test readiness probe
    curl -f "http://$service/actuator/health/readiness" || echo "Readiness check failed for $service"
    
    # Test liveness probe
    curl -f "http://$service/actuator/health/liveness" || echo "Liveness check failed for $service"
    
    echo "---"
done

# Test gateway aggregated health
echo "Testing gateway aggregated health..."
curl -f "http://gateway:8080/health/services" || echo "Gateway services health check failed"
curl -f "http://gateway:8080/health/summary" || echo "Gateway summary health check failed"
```

## Best Practices

1. **Always enable actuator health endpoints** in all microservices
2. **Use custom health indicators** for business-specific health checks
3. **Configure appropriate timeouts** for health checks
4. **Set up monitoring and alerting** based on health status
5. **Test health checks regularly** in all environments
6. **Document health check endpoints** and their meanings
7. **Use circuit breakers** to handle downstream service failures gracefully
